# FAQ标注任务API接口文档

## 概述

本文档描述了FAQ标注任务功能的所有API接口，包括任务管理、数据标注、统计查询和数据导出等功能。

## 接口列表

### 1. 新建标注任务

**接口地址：** `POST /faq-hit-log/annotation-task-create`

**功能描述：** 创建新的FAQ标注任务，抽取指定条件的数据

**请求参数：**
```json
{
  "taskName": "任务标注-20250722143000",
  "robotId": "robot123",
  "robotName": "测试机器人",
  "dataSource": "test",
  "callType": "all",
  "startTime": "2025-07-21T00:00:00",
  "endTime": "2025-07-22T00:00:00",
  "creatorId": "user123",
  "creatorName": "张三"
}
```

**响应结果：** 返回创建的任务ID

### 2. 任务标注列表查询

**接口地址：** `POST /faq-hit-log/annotation-task-list`

**功能描述：** 查询标注任务列表，支持搜索和筛选

**请求参数：**
```json
{
  "taskName": "测试",
  "status": "processing",
  "robotId": "robot123",
  "creatorId": "user123",
  "pageNum": 1,
  "pageSize": 10
}
```

### 3. 任务详情列表查询

**接口地址：** `POST /faq-hit-log/annotation-task-detail`

**功能描述：** 查询标注任务的详细数据列表

**请求参数：**
```json
{
  "annotationTaskId": "task123",
  "annotationType": "correct",
  "isLocked": false,
  "pageNum": 1,
  "pageSize": 10
}
```

### 4. 数据统计查询

**接口地址：** `GET /faq-hit-log/annotation-statistics/{taskId}`

**功能描述：** 查询标注任务的统计信息

**路径参数：**
- taskId: 任务ID

### 5. 任务标注（修改状态）

**接口地址：** `POST /faq-hit-log/annotation-mark`

**功能描述：** 对单条数据进行标注操作

**请求参数：**
```json
{
  "hitLogId": "log123",
  "annotationType": "correct",
  "annotationSubtype": "error_modify_knowledge",
  "annotatorId": "user123",
  "annotatorName": "张三"
}
```

### 6. 重新标注

**接口地址：** `POST /faq-hit-log/annotation-unlock/{hitLogId}`

**功能描述：** 解锁已标注的数据，允许重新标注

**路径参数：**
- hitLogId: 命中日志ID

### 7. 完成标注任务

**接口地址：** `POST /faq-hit-log/annotation-task-complete/{taskId}`

**功能描述：** 将标注任务状态修改为已完成

**路径参数：**
- taskId: 任务ID

### 8. 删除标注任务

**接口地址：** `POST /faq-hit-log/annotation-task-delete/{taskId}`

**功能描述：** 删除已完成的标注任务

**路径参数：**
- taskId: 任务ID

### 9. 导出标注任务数据

**接口地址：** `GET /faq-hit-log/annotation-task-export/{taskId}`

**功能描述：** 导出标注任务的Excel数据

**路径参数：**
- taskId: 任务ID

**响应：** Excel文件下载

## 标注类型说明

### 主要标注类型
- `correct`: 正确
- `error`: 错误
- `uncovered`: 未覆盖
- `invalid`: 无效
- `pending`: 待定

### 错误子类型
- `error_no_handle`: 错误暂不处理
- `error_modify_knowledge`: 错误修改知识
- `error_handled`: 错误已处理

### 未覆盖子类型
- `uncovered_no_handle`: 未覆盖暂不处理
- `uncovered_add_knowledge`: 未覆盖新增知识
- `uncovered_handled`: 未覆盖已处理

## 数据来源说明

- `all`: 全部环境
- `prod`: 正式环境
- `test`: 测试环境

## 任务状态说明

- `processing`: 进行中
- `completed`: 已完成

## 注意事项

1. 数据抽取时间跨度最大为1个月
2. 数据量最大为10000条
3. 只有已完成的任务才能删除
4. 标注后的数据会被锁定，需要重新标注才能修改
5. 导出功能包含完整的标注信息和统计数据
