package com.faw.work.ais.aic.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.faw.work.ais.aic.model.request.FaqAnnotationTaskCreateRequest;
import com.faw.work.ais.aic.model.request.FaqAnnotationTaskQueryRequest;
import com.faw.work.ais.aic.model.request.FaqHitLogAnnotationRequest;
import com.faw.work.ais.aic.service.FaqAnnotationTaskService;
import com.faw.work.ais.aic.service.FaqHitLogService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;
import java.util.ArrayList;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * FAQ命中日志控制器测试类
 *
 * <AUTHOR>
 */
@WebMvcTest(FaqHitLogController.class)
class FaqHitLogControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private FaqAnnotationTaskService faqAnnotationTaskService;

    @MockBean
    private FaqHitLogService faqHitLogService;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void testCreateAnnotationTask() throws Exception {
        // 准备测试数据
        FaqAnnotationTaskCreateRequest request = new FaqAnnotationTaskCreateRequest();
        request.setTaskName("测试标注任务");
        request.setRobotId("robot123");
        // request.setRobotName("测试机器人");
        request.setDataSource("test");
        request.setCallType("all");
        request.setStartTime(LocalDateTime.now().minusDays(1));
        request.setEndTime(LocalDateTime.now());
        // request.setCreatorId("user123");
        // request.setCreatorName("测试用户");

        // Mock服务方法
        when(faqAnnotationTaskService.createAnnotationTask(any())).thenReturn("task123");

        // 执行测试
        mockMvc.perform(post("/faq-hit-log/annotation-task-create")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(content().string("task123"));
    }

    @Test
    void testGetAnnotationTaskList() throws Exception {
        // 准备测试数据
        FaqAnnotationTaskQueryRequest request = new FaqAnnotationTaskQueryRequest();
        request.setTaskName("测试");
        request.setStatus("processing");
        request.setPageNum(1);
        request.setPageSize(10);

        // Mock服务方法
        when(faqAnnotationTaskService.getAnnotationTaskList(any())).thenReturn(new ArrayList<>());

        // 执行测试
        mockMvc.perform(post("/faq-hit-log/annotation-task-list")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray());
    }

    @Test
    void testMarkAnnotation() throws Exception {
        // 准备测试数据
        FaqHitLogAnnotationRequest request = new FaqHitLogAnnotationRequest();
        request.setHitLogId("log123");
        request.setAnnotationType("correct");
        request.setAnnotatorId("user123");
        request.setAnnotatorName("测试用户");

        // Mock服务方法
        when(faqHitLogService.markAnnotation(any(), any(), any(), any(), any())).thenReturn(true);

        // 执行测试
        mockMvc.perform(post("/faq-hit-log/annotation-mark")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(content().string("true"));
    }

    @Test
    void testGetAnnotationStatistics() throws Exception {
        // Mock服务方法
        when(faqHitLogService.getAnnotationStatistics(any())).thenReturn(null);

        // 执行测试
        mockMvc.perform(get("/faq-hit-log/annotation-statistics/task123"))
                .andExpect(status().isOk());
    }

    @Test
    void testUnlockAnnotation() throws Exception {
        // Mock服务方法
        when(faqHitLogService.unlockAnnotation(any())).thenReturn(true);

        // 执行测试
        mockMvc.perform(post("/faq-hit-log/annotation-unlock/log123"))
                .andExpect(status().isOk())
                .andExpect(content().string("true"));
    }

    @Test
    void testCompleteAnnotationTask() throws Exception {
        // Mock服务方法
        when(faqAnnotationTaskService.completeTask(any())).thenReturn(true);

        // 执行测试
        mockMvc.perform(post("/faq-hit-log/annotation-task-complete/task123"))
                .andExpect(status().isOk())
                .andExpect(content().string("true"));
    }
}
