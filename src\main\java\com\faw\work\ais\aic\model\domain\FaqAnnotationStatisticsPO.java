package com.faw.work.ais.aic.model.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * FAQ标注统计实体类
 *
 * <AUTHOR>
 */
@Data
@TableName("faq_annotation_statistics")
@Accessors(chain = true)
public class FaqAnnotationStatisticsPO {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    @TableField("task_id")
    private String taskId;

    @TableField("annotation_type")
    private String annotationType;

    @TableField("annotation_subtype")
    private String annotationSubtype;

    @TableField("count")
    private Integer count;

    @TableField("created_at")
    private LocalDateTime createdAt;

    @TableField("updated_at")
    private LocalDateTime updatedAt;
}
