package com.faw.work.ais.aic.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.faw.work.ais.aic.mapper.faq.FaqHitLogMapper;
import com.faw.work.ais.aic.model.domain.FaqHitLogPO;
import com.faw.work.ais.aic.model.response.FaqHitLogCleanResponse;
import com.faw.work.ais.aic.service.FaqHitLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * FAQ命中日志Service实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class FaqHitLogServiceImpl implements FaqHitLogService {

    @Autowired
    private FaqHitLogMapper faqHitLogMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FaqHitLogCleanResponse cleanOldHitLogs() {
        log.info("开始清理一个月前的FAQ命中日志数据");

        LocalDateTime cleanTime = LocalDateTime.now();
        int cleanCount = faqHitLogMapper.cleanOldHitLogs();

        log.info("FAQ命中日志清理完成，共清理{}条记录", cleanCount);

        return FaqHitLogCleanResponse.builder()
                .cleanCount(cleanCount)
                .cleanTime(cleanTime)
                .description("成功清理一个月前的FAQ命中日志数据")
                .build();
    }

    @Override
    public List<FaqHitLogPO> findHitLogsByCondition(String robotId, String dataSource,
                                                   LocalDateTime startTime, LocalDateTime endTime) {
        if (StrUtil.isBlank(robotId)) {
            throw new IllegalArgumentException("机器人ID不能为空");
        }
        if (ObjectUtil.isNull(startTime) || ObjectUtil.isNull(endTime)) {
            throw new IllegalArgumentException("开始时间和结束时间不能为空");
        }

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String startTimeStr = startTime.format(formatter);
        String endTimeStr = endTime.format(formatter);

        List<FaqHitLogPO> hitLogs = faqHitLogMapper.findByCondition(robotId, dataSource, startTimeStr, endTimeStr);
        log.info("查询FAQ命中日志数据，机器人ID: {}, 数据来源: {}, 查询数量: {}", robotId, dataSource, hitLogs.size());

        return hitLogs;
    }
}
        if (StrUtil.isBlank(robotId)) {
            throw new IllegalArgumentException("机器人ID不能为空");
        }
        if (StrUtil.isBlank(annotationTaskId)) {
            throw new IllegalArgumentException("标注任务ID不能为空");
        }
        if (ObjectUtil.isNull(startTime) || ObjectUtil.isNull(endTime)) {
            throw new IllegalArgumentException("开始时间和结束时间不能为空");
        }

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String startTimeStr = startTime.format(formatter);
        String endTimeStr = endTime.format(formatter);

        int updateCount = faqHitLogMapper.updateAnnotationTaskId(robotId, dataSource, startTimeStr, endTimeStr, annotationTaskId);
        log.info("成功抽取FAQ命中日志数据，机器人ID: {}, 数据来源: {}, 抽取数量: {}", robotId, dataSource, updateCount);

        return updateCount;
    }

    @Override
    public List<FaqHitLogDetailResponse> getAnnotationTaskDetail(FaqHitLogDetailQueryRequest request) {
        if (StrUtil.isBlank(request.getAnnotationTaskId())) {
            throw new IllegalArgumentException("标注任务ID不能为空");
        }

        int offset = (request.getPageNum() - 1) * request.getPageSize();
        List<FaqHitLogPO> hitLogs = faqHitLogMapper.findByAnnotationTaskId(
                request.getAnnotationTaskId(),
                request.getAnnotationType(),
                request.getIsLocked(),
                offset,
                request.getPageSize()
        );

        List<FaqHitLogDetailResponse> responses = new ArrayList<>();
        for (FaqHitLogPO hitLog : hitLogs) {
            FaqHitLogDetailResponse response = new FaqHitLogDetailResponse();
            BeanUtils.copyProperties(hitLog, response);

            // 设置匹配类型
            if (hitLog.getIsHit() != null && hitLog.getIsHit()) {
                response.setMatchType("has_answer");
            } else {
                response.setMatchType("no_answer");
            }

            responses.add(response);
        }

        return responses;
    }

    @Override
    public FaqAnnotationStatisticsResponse getAnnotationStatistics(String taskId) {
        if (StrUtil.isBlank(taskId)) {
            throw new IllegalArgumentException("任务ID不能为空");
        }

        // 统计总数据量和已标注数量
        int totalCount = faqHitLogMapper.countByAnnotationTaskId(taskId);
        int annotatedCount = faqHitLogMapper.countAnnotatedByTaskId(taskId);

        // 统计各种标注类型的数量
        List<FaqAnnotationStatisticsResponse.AnnotationTypeStatistics> annotationList = new ArrayList<>();

        // 正确类型统计
        int correctCount = faqHitLogMapper.countByAnnotationType(taskId, "correct", null);
        if (correctCount > 0) {
            annotationList.add(FaqAnnotationStatisticsResponse.AnnotationTypeStatistics.builder()
                    .annotationType("correct")
                    .annotationTypeName("正确")
                    .count(correctCount)
                    .build());
        }

        // 错误类型统计
        int errorCount = faqHitLogMapper.countByAnnotationType(taskId, "error", null);
        if (errorCount > 0) {
            List<FaqAnnotationStatisticsResponse.AnnotationSubtypeStatistics> errorSubtypes = new ArrayList<>();

            int errorNoHandle = faqHitLogMapper.countByAnnotationType(taskId, "error", "error_no_handle");
            if (errorNoHandle > 0) {
                errorSubtypes.add(FaqAnnotationStatisticsResponse.AnnotationSubtypeStatistics.builder()
                        .annotationSubtype("error_no_handle")
                        .annotationSubtypeName("错误暂不处理")
                        .count(errorNoHandle)
                        .build());
            }

            int errorModifyKnowledge = faqHitLogMapper.countByAnnotationType(taskId, "error", "error_modify_knowledge");
            if (errorModifyKnowledge > 0) {
                errorSubtypes.add(FaqAnnotationStatisticsResponse.AnnotationSubtypeStatistics.builder()
                        .annotationSubtype("error_modify_knowledge")
                        .annotationSubtypeName("错误修改知识")
                        .count(errorModifyKnowledge)
                        .build());
            }

            int errorHandled = faqHitLogMapper.countByAnnotationType(taskId, "error", "error_handled");
            if (errorHandled > 0) {
                errorSubtypes.add(FaqAnnotationStatisticsResponse.AnnotationSubtypeStatistics.builder()
                        .annotationSubtype("error_handled")
                        .annotationSubtypeName("错误已处理")
                        .count(errorHandled)
                        .build());
            }

            annotationList.add(FaqAnnotationStatisticsResponse.AnnotationTypeStatistics.builder()
                    .annotationType("error")
                    .annotationTypeName("错误")
                    .count(errorCount)
                    .subtypeList(errorSubtypes)
                    .build());
        }

        // 未覆盖类型统计
        int uncoveredCount = faqHitLogMapper.countByAnnotationType(taskId, "uncovered", null);
        if (uncoveredCount > 0) {
            List<FaqAnnotationStatisticsResponse.AnnotationSubtypeStatistics> uncoveredSubtypes = new ArrayList<>();

            int uncoveredNoHandle = faqHitLogMapper.countByAnnotationType(taskId, "uncovered", "uncovered_no_handle");
            if (uncoveredNoHandle > 0) {
                uncoveredSubtypes.add(FaqAnnotationStatisticsResponse.AnnotationSubtypeStatistics.builder()
                        .annotationSubtype("uncovered_no_handle")
                        .annotationSubtypeName("未覆盖暂不处理")
                        .count(uncoveredNoHandle)
                        .build());
            }

            int uncoveredAddKnowledge = faqHitLogMapper.countByAnnotationType(taskId, "uncovered", "uncovered_add_knowledge");
            if (uncoveredAddKnowledge > 0) {
                uncoveredSubtypes.add(FaqAnnotationStatisticsResponse.AnnotationSubtypeStatistics.builder()
                        .annotationSubtype("uncovered_add_knowledge")
                        .annotationSubtypeName("未覆盖新增知识")
                        .count(uncoveredAddKnowledge)
                        .build());
            }

            int uncoveredHandled = faqHitLogMapper.countByAnnotationType(taskId, "uncovered", "uncovered_handled");
            if (uncoveredHandled > 0) {
                uncoveredSubtypes.add(FaqAnnotationStatisticsResponse.AnnotationSubtypeStatistics.builder()
                        .annotationSubtype("uncovered_handled")
                        .annotationSubtypeName("未覆盖已处理")
                        .count(uncoveredHandled)
                        .build());
            }

            annotationList.add(FaqAnnotationStatisticsResponse.AnnotationTypeStatistics.builder()
                    .annotationType("uncovered")
                    .annotationTypeName("未覆盖")
                    .count(uncoveredCount)
                    .subtypeList(uncoveredSubtypes)
                    .build());
        }

        // 无效类型统计
        int invalidCount = faqHitLogMapper.countByAnnotationType(taskId, "invalid", null);
        if (invalidCount > 0) {
            annotationList.add(FaqAnnotationStatisticsResponse.AnnotationTypeStatistics.builder()
                    .annotationType("invalid")
                    .annotationTypeName("无效")
                    .count(invalidCount)
                    .build());
        }

        // 待定类型统计
        int pendingCount = faqHitLogMapper.countByAnnotationType(taskId, "pending", null);
        if (pendingCount > 0) {
            annotationList.add(FaqAnnotationStatisticsResponse.AnnotationTypeStatistics.builder()
                    .annotationType("pending")
                    .annotationTypeName("待定")
                    .count(pendingCount)
                    .build());
        }

        return FaqAnnotationStatisticsResponse.builder()
                .totalCount(totalCount)
                .annotatedCount(annotatedCount)
                .annotationList(annotationList)
                .build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean markAnnotation(String hitLogId, String annotationType, String annotationSubtype,
                                 String annotatorId, String annotatorName) {
        if (StrUtil.isBlank(hitLogId)) {
            throw new IllegalArgumentException("命中日志ID不能为空");
        }
        if (StrUtil.isBlank(annotationType)) {
            throw new IllegalArgumentException("标注类型不能为空");
        }
        if (StrUtil.isBlank(annotatorId)) {
            throw new IllegalArgumentException("标注人ID不能为空");
        }
        if (StrUtil.isBlank(annotatorName)) {
            throw new IllegalArgumentException("标注人姓名不能为空");
        }

        int updateCount = faqHitLogMapper.updateAnnotation(hitLogId, annotationType, annotationSubtype, annotatorId, annotatorName);
        if (updateCount > 0) {
            log.info("成功标注数据，日志ID: {}, 标注类型: {}, 标注人: {}", hitLogId, annotationType, annotatorName);
            return true;
        }

        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean unlockAnnotation(String hitLogId) {
        if (StrUtil.isBlank(hitLogId)) {
            throw new IllegalArgumentException("命中日志ID不能为空");
        }

        int updateCount = faqHitLogMapper.unlockAnnotation(hitLogId);
        if (updateCount > 0) {
            log.info("成功解锁标注数据，日志ID: {}", hitLogId);
            return true;
        }

        return false;
    }

    @Override
    public List<FaqHitLogPO> getAnnotationDataForExport(String taskId) {
        if (StrUtil.isBlank(taskId)) {
            throw new IllegalArgumentException("任务ID不能为空");
        }

        return faqHitLogMapper.findAllByAnnotationTaskId(taskId);
    }
}
