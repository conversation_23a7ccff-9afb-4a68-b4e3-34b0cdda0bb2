package com.faw.work.ais.aic.service;

import com.faw.work.ais.aic.model.domain.FaqHitLogPO;
import com.faw.work.ais.aic.model.request.FaqHitLogDetailQueryRequest;
import com.faw.work.ais.aic.model.response.FaqAnnotationStatisticsResponse;
import com.faw.work.ais.aic.model.response.FaqHitLogCleanResponse;
import com.faw.work.ais.aic.model.response.FaqHitLogDetailResponse;

import java.time.LocalDateTime;
import java.util.List;

/**
 * FAQ命中日志Service接口
 *
 * <AUTHOR>
 */
public interface FaqHitLogService {

    /**
     * 清理一个月前的FAQ命中日志数据
     *
     * @return 清理结果
     */
    FaqHitLogCleanResponse cleanOldHitLogs();

    /**
     * 根据条件抽取FAQ命中日志数据并关联到标注任务
     *
     * @param robotId 机器人ID
     * @param dataSource 数据来源
     * @param callType 通话类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param annotationTaskId 标注任务ID
     * @return 抽取的数据数量
     */
    Integer extractHitLogsForAnnotation(String robotId, String dataSource, String callType,
                                       LocalDateTime startTime, LocalDateTime endTime, String annotationTaskId);

    /**
     * 查询标注任务的详细数据列表
     *
     * @param request 查询请求参数
     * @return 详细数据列表
     */
    List<FaqHitLogDetailResponse> getAnnotationTaskDetail(FaqHitLogDetailQueryRequest request);

    /**
     * 查询标注任务的统计信息
     *
     * @param taskId 任务ID
     * @return 统计信息
     */
    FaqAnnotationStatisticsResponse getAnnotationStatistics(String taskId);

    /**
     * 标注单条数据
     *
     * @param hitLogId 命中日志ID
     * @param annotationType 标注类型
     * @param annotationSubtype 标注子类型
     * @param annotatorId 标注人ID
     * @param annotatorName 标注人姓名
     * @return 是否标注成功
     */
    Boolean markAnnotation(String hitLogId, String annotationType, String annotationSubtype,
                          String annotatorId, String annotatorName);

    /**
     * 重新标注（解锁）
     *
     * @param hitLogId 命中日志ID
     * @return 是否解锁成功
     */
    Boolean unlockAnnotation(String hitLogId);

    /**
     * 根据任务ID查询所有标注数据用于导出
     *
     * @param taskId 任务ID
     * @return 标注数据列表
     */
    List<FaqHitLogPO> getAnnotationDataForExport(String taskId);
}
