package com.faw.work.ais.aic.mapper.faq;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.faw.work.ais.aic.model.domain.FaqAnnotationTaskPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * FAQ标注任务Mapper接口
 * 提供FAQ标注任务相关的数据库操作
 *
 * <AUTHOR>
 */
@Mapper
public interface FaqAnnotationTaskMapper extends BaseMapper<FaqAnnotationTaskPO> {

    /**
     * 根据机器人ID查询标注任务列表
     *
     * @param robotId 机器人ID，不能为空
     * @return 标注任务列表
     */
    List<FaqAnnotationTaskPO> findByRobotId(@Param("robotId") String robotId);

    /**
     * 根据创建人ID查询标注任务列表
     *
     * @param creatorId 创建人ID，不能为空
     * @return 标注任务列表
     */
    List<FaqAnnotationTaskPO> findByCreatorId(@Param("creatorId") String creatorId);

    /**
     * 根据状态查询标注任务列表
     *
     * @param status 任务状态，不能为空
     * @return 标注任务列表
     */
    List<FaqAnnotationTaskPO> findByStatus(@Param("status") String status);

    /**
     * 更新任务状态为已完成
     *
     * @param taskId 任务ID，不能为空
     * @return 更新的记录数
     */
    int updateStatusToCompleted(@Param("taskId") String taskId);

    /**
     * 更新已标注数量
     *
     * @param taskId 任务ID，不能为空
     * @param annotatedCount 已标注数量
     * @return 更新的记录数
     */
    int updateAnnotatedCount(@Param("taskId") String taskId, @Param("annotatedCount") Integer annotatedCount);
}
