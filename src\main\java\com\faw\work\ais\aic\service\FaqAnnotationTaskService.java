package com.faw.work.ais.aic.service;

import com.faw.work.ais.aic.model.domain.FaqAnnotationTaskPO;
import com.faw.work.ais.aic.model.request.FaqAnnotationTaskCreateRequest;
import com.faw.work.ais.aic.model.request.FaqAnnotationTaskQueryRequest;
import com.faw.work.ais.aic.model.response.FaqAnnotationTaskResponse;

import java.util.List;

/**
 * FAQ标注任务Service接口
 *
 * <AUTHOR>
 */
public interface FaqAnnotationTaskService {

    /**
     * 创建标注任务
     *
     * @param task 标注任务信息
     * @return 创建的任务ID
     */
    String createTask(FaqAnnotationTaskPO task);

    /**
     * 根据任务ID查询标注任务
     *
     * @param taskId 任务ID
     * @return 标注任务信息
     */
    FaqAnnotationTaskPO getTaskById(String taskId);

    /**
     * 根据机器人ID查询标注任务列表
     *
     * @param robotId 机器人ID
     * @return 标注任务列表
     */
    List<FaqAnnotationTaskPO> getTasksByRobotId(String robotId);

    /**
     * 根据创建人ID查询标注任务列表
     *
     * @param creatorId 创建人ID
     * @return 标注任务列表
     */
    List<FaqAnnotationTaskPO> getTasksByCreatorId(String creatorId);

    /**
     * 根据状态查询标注任务列表
     *
     * @param status 任务状态
     * @return 标注任务列表
     */
    List<FaqAnnotationTaskPO> getTasksByStatus(String status);

    /**
     * 更新任务状态为已完成
     *
     * @param taskId 任务ID
     * @return 是否更新成功
     */
    boolean completeTask(String taskId);

    /**
     * 更新已标注数量
     *
     * @param taskId 任务ID
     * @param annotatedCount 已标注数量
     * @return 是否更新成功
     */
    boolean updateAnnotatedCount(String taskId, Integer annotatedCount);

    /**
     * 删除标注任务
     *
     * @param taskId 任务ID
     * @return 是否删除成功
     */
    boolean deleteTask(String taskId);

    /**
     * 创建标注任务（包含数据抽取）
     *
     * @param request 创建请求参数
     * @return 创建的任务ID
     */
    String createAnnotationTask(FaqAnnotationTaskCreateRequest request);

    /**
     * 分页查询标注任务列表
     *
     * @param request 查询请求参数
     * @return 任务列表
     */
    List<FaqAnnotationTaskResponse> getAnnotationTaskList(FaqAnnotationTaskQueryRequest request);

    /**
     * 检查任务是否可以删除（只有已完成的任务才能删除）
     *
     * @param taskId 任务ID
     * @return 是否可以删除
     */
    boolean canDeleteTask(String taskId);
}
