package com.faw.work.ais.aic.service;

import com.faw.work.ais.aic.model.domain.FaqAnnotationTaskPO;

import java.util.List;

/**
 * FAQ标注任务Service接口
 *
 * <AUTHOR>
 */
public interface FaqAnnotationTaskService {

    /**
     * 创建标注任务
     *
     * @param task 标注任务信息
     * @return 创建的任务ID
     */
    String createTask(FaqAnnotationTaskPO task);

    /**
     * 根据任务ID查询标注任务
     *
     * @param taskId 任务ID
     * @return 标注任务信息
     */
    FaqAnnotationTaskPO getTaskById(String taskId);

    /**
     * 根据机器人ID查询标注任务列表
     *
     * @param robotId 机器人ID
     * @return 标注任务列表
     */
    List<FaqAnnotationTaskPO> getTasksByRobotId(String robotId);

    /**
     * 根据创建人ID查询标注任务列表
     *
     * @param creatorId 创建人ID
     * @return 标注任务列表
     */
    List<FaqAnnotationTaskPO> getTasksByCreatorId(String creatorId);

    /**
     * 根据状态查询标注任务列表
     *
     * @param status 任务状态
     * @return 标注任务列表
     */
    List<FaqAnnotationTaskPO> getTasksByStatus(String status);

    /**
     * 更新任务状态为已完成
     *
     * @param taskId 任务ID
     * @return 是否更新成功
     */
    boolean completeTask(String taskId);

    /**
     * 更新已标注数量
     *
     * @param taskId 任务ID
     * @param annotatedCount 已标注数量
     * @return 是否更新成功
     */
    boolean updateAnnotatedCount(String taskId, Integer annotatedCount);

    /**
     * 删除标注任务
     *
     * @param taskId 任务ID
     * @return 是否删除成功
     */
    boolean deleteTask(String taskId);
}
