package com.faw.work.ais.aic.mapper.faq;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.faw.work.ais.aic.model.domain.FaqHitLogPO;
import com.faw.work.ais.aic.model.response.FaqReportResponse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * FAQ命中日志Mapper接口
 * 提供FAQ命中日志相关的数据库操作
 *
 * <AUTHOR>
 */
@Mapper
public interface FaqHitLogMapper extends BaseMapper<FaqHitLogPO> {

    /**
     * 统计有效知识命中数量
     *
     * @param robotId   机器人ID，不能为空
     * @param env       环境标识（如：prod/test/dev等），不能为空
     * @param startTime 开始时间（格式：yyyy-MM-dd HH:mm:ss），可为空表示不限制开始时间
     * @param endTime   结束时间（格式：yyyy-MM-dd HH:mm:ss），可为空表示不限制结束时间
     * @return 有效知识命中数量（去重统计）
     */
    Long countHitEffectiveKnowledge(@Param("robotId") String robotId,
                                    @Param("env") String env,
                                    @Param("startTime") String startTime,
                                    @Param("endTime") String endTime);

    /**
     * 统计总会话量（可根据是否命中过滤）
     *
     * @param robotId   机器人ID，不能为空
     * @param env       环境标识，不能为空
     * @param startTime 开始时间，可为空
     * @param endTime   结束时间，可为空
     * @param isHit     是否命中标识（1:命中/0:未命中），不能为空
     * @return 符合条件的会话数量（去重统计chat_id）
     */
    Long countTotalChat(@Param("robotId") String robotId,
                        @Param("env") String env,
                        @Param("startTime") String startTime,
                        @Param("endTime") String endTime,
                        @Param("isHit") String isHit);

    /**
     * 查询TOP10命中知识排行
     *
     * @param robotId   机器人ID，不能为空
     * @param env       环境标识，不能为空
     * @param startTime 开始时间，可为空
     * @param endTime   结束时间，可为空
     * @return 命中知识排行列表（按命中次数降序，最多返回10条）
     */
    List<FaqReportResponse.FaqHitRankItem> findTop10HitKnowledge(@Param("robotId") String robotId,
                                                                 @Param("env") String env,
                                                                 @Param("startTime") String startTime,
                                                                 @Param("endTime") String endTime);

    /**
     * 查询时间范围内的命中知识ID列表
     *
     * @param robotId   机器人ID，不能为空
     * @param env       环境标识，不能为空
     * @param startTime 开始时间，可为空
     * @param endTime   结束时间，可为空
     * @return 命中知识ID列表（未去重）
     */
    List<String> findHitKnowledgeIdsInDateRange(@Param("robotId") String robotId,
                                                @Param("env") String env,
                                                @Param("startTime") String startTime,
                                                @Param("endTime") String endTime);

    /**
     * 统计有效原始知识命中数量
     *
     * @param robotId   机器人ID，不能为空
     * @param env       环境标识（如：prod/test/dev等），不能为空
     * @param startTime 开始时间（格式：yyyy-MM-dd HH:mm:ss），可为空表示不限制开始时间
     * @param endTime   结束时间（格式：yyyy-MM-dd HH:mm:ss），可为空表示不限制结束时间
     * @return 有效原始知识命中数量（去重统计）
     */
    Long countHitEffectiveOriginalKnowledge(@Param("robotId") String robotId,
                                            @Param("env") String env,
                                            @Param("startTime") String startTime,
                                            @Param("endTime") String endTime);

    /**
     * 查询时间范围内的命中原始知识ID列表
     *
     * @param robotId   机器人ID，不能为空
     * @param env       环境标识，不能为空
     * @param startTime 开始时间，可为空
     * @param endTime   结束时间，可为空
     * @return 命中原始知识ID列表（未去重）
     */
    List<String> findHitOriginalKnowledgeIdsInDateRange(@Param("robotId") String robotId,
                                                        @Param("env") String env,
                                                        @Param("startTime") String startTime,
                                                        @Param("endTime") String endTime);

    /**
     * 按知识id计数
     *
     * @param id 本我
     * @return long
     */
    long countByKnowledgeId(String id);

    /**
     * 清理一个月前的FAQ命中日志数据
     *
     * @return 清理的记录数量
     */
    int cleanOldHitLogs();

    /**
     * 根据条件查询FAQ命中日志并更新标注任务ID
     *
     * @param robotId 机器人ID
     * @param dataSource 数据来源
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param annotationTaskId 标注任务ID
     * @return 更新的记录数量
     */
    int updateAnnotationTaskId(@Param("robotId") String robotId,
                              @Param("dataSource") String dataSource,
                              @Param("startTime") String startTime,
                              @Param("endTime") String endTime,
                              @Param("annotationTaskId") String annotationTaskId);

    /**
     * 根据标注任务ID分页查询命中日志详情
     *
     * @param annotationTaskId 标注任务ID
     * @param annotationType 标注类型筛选
     * @param isLocked 是否锁定筛选
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 命中日志列表
     */
    List<FaqHitLogPO> findByAnnotationTaskId(@Param("annotationTaskId") String annotationTaskId,
                                            @Param("annotationType") String annotationType,
                                            @Param("isLocked") Boolean isLocked,
                                            @Param("offset") Integer offset,
                                            @Param("limit") Integer limit);

    /**
     * 统计标注任务的总数据量
     *
     * @param annotationTaskId 标注任务ID
     * @return 总数据量
     */
    int countByAnnotationTaskId(@Param("annotationTaskId") String annotationTaskId);

    /**
     * 统计标注任务的已标注数量
     *
     * @param annotationTaskId 标注任务ID
     * @return 已标注数量
     */
    int countAnnotatedByTaskId(@Param("annotationTaskId") String annotationTaskId);

    /**
     * 根据标注类型统计数量
     *
     * @param annotationTaskId 标注任务ID
     * @param annotationType 标注类型
     * @param annotationSubtype 标注子类型
     * @return 统计数量
     */
    int countByAnnotationType(@Param("annotationTaskId") String annotationTaskId,
                             @Param("annotationType") String annotationType,
                             @Param("annotationSubtype") String annotationSubtype);

    /**
     * 更新标注信息
     *
     * @param hitLogId 命中日志ID
     * @param annotationType 标注类型
     * @param annotationSubtype 标注子类型
     * @param annotatorId 标注人ID
     * @param annotatorName 标注人姓名
     * @return 更新的记录数量
     */
    int updateAnnotation(@Param("hitLogId") String hitLogId,
                        @Param("annotationType") String annotationType,
                        @Param("annotationSubtype") String annotationSubtype,
                        @Param("annotatorId") String annotatorId,
                        @Param("annotatorName") String annotatorName);

    /**
     * 解锁标注（重新标注）
     *
     * @param hitLogId 命中日志ID
     * @return 更新的记录数量
     */
    int unlockAnnotation(@Param("hitLogId") String hitLogId);

    /**
     * 根据任务ID查询所有标注数据
     *
     * @param annotationTaskId 标注任务ID
     * @return 标注数据列表
     */
    List<FaqHitLogPO> findAllByAnnotationTaskId(@Param("annotationTaskId") String annotationTaskId);
}
