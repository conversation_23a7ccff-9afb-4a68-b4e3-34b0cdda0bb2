package com.faw.work.ais.aic.model.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * FAQ命中日志实体类
 *
 * <AUTHOR>
 */
@Data
@TableName("faq_hit_log")
@Accessors(chain = true)
public class FaqHitLogPO {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    @TableField("robot_id")
    private String robotId;

    @TableField("knowledge_id")
    private String knowledgeId;

    @TableField("hit_time")
    private LocalDateTime hitTime;

    @TableField("environment")
    private String environment;

    @TableField("chat_id")
    private String chatId;

    @TableField("session_id")
    private String sessionId;

    @TableField("is_effective")
    private Boolean isEffective;

    @TableField("is_hit")
    private Boolean isHit;

    @TableField("created_at")
    private LocalDateTime createdAt;

    @TableField("user_question")
    private String userQuestion;

    @TableField("matched_answer")
    private String matchedAnswer;

    @TableField("match_score")
    private BigDecimal matchScore;

    @TableField("faq_title")
    private String faqTitle;

    @TableField("annotation_task_id")
    private String annotationTaskId;

    @TableField("annotation_type")
    private String annotationType;

    @TableField("annotation_subtype")
    private String annotationSubtype;

    @TableField("is_locked")
    private Boolean isLocked;

    @TableField("annotator_id")
    private String annotatorId;

    @TableField("annotator_name")
    private String annotatorName;

    @TableField("annotated_at")
    private LocalDateTime annotatedAt;

    @TableField("updated_at")
    private LocalDateTime updatedAt;
}