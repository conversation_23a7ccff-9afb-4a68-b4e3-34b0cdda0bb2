# FAQ标注任务功能部署指南

## 📋 部署前检查清单

### 1. 数据库准备
- [ ] 确认MySQL数据库版本 >= 8.0
- [ ] 确认应用有数据库的DDL权限
- [ ] 备份现有的faq_hit_log表数据（如有重要数据）

### 2. 依赖检查
- [ ] 确认Apache POI依赖已添加（用于Excel导出）
- [ ] 确认MyBatis-Plus版本兼容
- [ ] 确认Hutool工具类依赖

## 🗄️ 数据库部署步骤

### 1. 执行表结构创建脚本
```sql
-- 1. 创建标注详情表
CREATE TABLE `faq_annotation_detail` (
  `id` varchar(64) NOT NULL COMMENT '主键ID',
  `task_id` varchar(64) DEFAULT NULL COMMENT '标注任务ID',
  `hit_log_id` varchar(64) DEFAULT NULL COMMENT '命中日志ID',
  `user_question` text COMMENT '用户问法',
  `match_type` varchar(20) DEFAULT NULL COMMENT '匹配类型：no_answer-无答案，has_answer-有答案',
  `matched_content` text COMMENT '匹配内容详情',
  `knowledge_id` varchar(64) DEFAULT NULL COMMENT '匹配到的知识ID',
  `faq_title` varchar(500) DEFAULT NULL COMMENT 'FAQ标题',
  `match_score` decimal(5,4) DEFAULT NULL COMMENT '匹配度分数',
  `annotation_type` varchar(30) DEFAULT NULL COMMENT '标注类型',
  `annotation_subtype` varchar(50) DEFAULT NULL COMMENT '标注子类型',
  `is_locked` tinyint(1) DEFAULT '0' COMMENT '是否锁定（已标注）',
  `annotator_id` varchar(64) DEFAULT NULL COMMENT '标注人ID',
  `annotator_name` varchar(100) DEFAULT NULL COMMENT '标注人姓名',
  `annotated_at` timestamp NULL DEFAULT NULL COMMENT '标注时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_hit_log_id` (`hit_log_id`),
  KEY `idx_annotation_type` (`annotation_type`),
  CONSTRAINT `faq_annotation_detail_ibfk_1` FOREIGN KEY (`task_id`) REFERENCES `faq_annotation_task` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='标注详情表';

-- 2. 创建标注统计表
CREATE TABLE `faq_annotation_statistics` (
  `id` varchar(64) NOT NULL COMMENT '主键ID',
  `task_id` varchar(64) DEFAULT NULL COMMENT '标注任务ID',
  `annotation_type` varchar(30) DEFAULT NULL COMMENT '标注类型',
  `annotation_subtype` varchar(50) DEFAULT NULL COMMENT '标注子类型',
  `count` int DEFAULT '0' COMMENT '数量',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_task_type_subtype` (`task_id`,`annotation_type`,`annotation_subtype`),
  KEY `idx_task_id` (`task_id`),
  CONSTRAINT `faq_annotation_statistics_ibfk_1` FOREIGN KEY (`task_id`) REFERENCES `faq_annotation_task` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='标注统计表';
```

### 2. 清理faq_hit_log表的标注字段（如果存在）
```sql
-- 如果之前的版本在faq_hit_log表中添加了标注相关字段，需要删除
ALTER TABLE faq_hit_log 
DROP COLUMN IF EXISTS annotation_task_id,
DROP COLUMN IF EXISTS annotation_type,
DROP COLUMN IF EXISTS annotation_subtype,
DROP COLUMN IF EXISTS is_locked,
DROP COLUMN IF EXISTS annotator_id,
DROP COLUMN IF EXISTS annotator_name,
DROP COLUMN IF EXISTS annotated_at;
```

## 🚀 应用部署步骤

### 1. 代码部署
- [ ] 部署新的Java代码
- [ ] 确认所有新增的类文件都已包含
- [ ] 确认XML映射文件路径正确

### 2. 配置检查
- [ ] 确认MyBatis扫描路径包含新的Mapper
- [ ] 确认Spring扫描路径包含新的Service
- [ ] 确认数据库连接配置正确

### 3. 启动验证
- [ ] 应用启动无错误
- [ ] 数据库连接正常
- [ ] 新增的表可以正常访问

## 🧪 功能测试清单

### 1. 基础功能测试
- [ ] 新建标注任务
- [ ] 查询任务列表
- [ ] 查看任务详情
- [ ] 数据统计查询

### 2. 标注功能测试
- [ ] 标注单条数据（正确）
- [ ] 标注单条数据（错误+子类型）
- [ ] 标注单条数据（未覆盖+子类型）
- [ ] 标注单条数据（无效）
- [ ] 标注单条数据（待定）

### 3. 高级功能测试
- [ ] 重新标注功能
- [ ] 任务完成功能
- [ ] Excel导出功能
- [ ] 任务删除功能（仅已完成任务）

### 4. 边界条件测试
- [ ] 大数据量任务创建（接近10000条限制）
- [ ] 时间跨度限制测试（超过1个月）
- [ ] 并发标注测试
- [ ] 统计数据准确性验证

## 📊 性能监控

### 1. 关键指标
- 任务创建响应时间
- 标注操作响应时间
- 统计查询响应时间
- 导出功能执行时间

### 2. 数据库监控
- 新表的查询性能
- 索引使用情况
- 外键约束性能影响

## 🔧 故障排查

### 1. 常见问题
- **外键约束错误**: 检查faq_annotation_task表是否存在
- **权限错误**: 确认应用有新表的操作权限
- **性能问题**: 检查索引是否正确创建

### 2. 日志关键字
- "成功创建标注任务"
- "批量创建标注详情"
- "成功标注数据"
- "重新计算统计信息"

## 📈 性能优化建议

### 1. 数据库优化
- 定期分析表统计信息
- 监控慢查询日志
- 考虑分区策略（大数据量场景）

### 2. 应用优化
- 启用查询缓存
- 优化批量操作大小
- 考虑异步处理大任务

## 🔄 回滚方案

如果部署出现问题，可以按以下步骤回滚：

1. **停止应用服务**
2. **恢复旧版本代码**
3. **删除新增的表**（如果没有重要数据）
4. **恢复faq_hit_log表结构**（如果有修改）
5. **重启应用服务**

## ✅ 部署完成确认

- [ ] 所有测试用例通过
- [ ] 性能指标正常
- [ ] 日志输出正常
- [ ] 用户验收通过
- [ ] 监控告警正常

部署完成后，FAQ标注任务功能将以全新的架构为用户提供更稳定、高效的服务！
