<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.faw.work.ais.aic.mapper.faq.FaqAnnotationTaskMapper">

    <!-- 根据机器人ID查询标注任务列表 -->
    <select id="findByRobotId" resultType="com.faw.work.ais.aic.model.domain.FaqAnnotationTaskPO">
        SELECT *
        FROM faq_annotation_task
        WHERE robot_id = #{robotId}
        ORDER BY created_at DESC
    </select>

    <!-- 根据创建人ID查询标注任务列表 -->
    <select id="findByCreatorId" resultType="com.faw.work.ais.aic.model.domain.FaqAnnotationTaskPO">
        SELECT *
        FROM faq_annotation_task
        WHERE creator_id = #{creatorId}
        ORDER BY created_at DESC
    </select>

    <!-- 根据状态查询标注任务列表 -->
    <select id="findByStatus" resultType="com.faw.work.ais.aic.model.domain.FaqAnnotationTaskPO">
        SELECT *
        FROM faq_annotation_task
        WHERE status = #{status}
        ORDER BY created_at DESC
    </select>

    <!-- 更新任务状态为已完成 -->
    <update id="updateStatusToCompleted">
        UPDATE faq_annotation_task
        SET status = 'completed',
            completed_at = NOW(),
            updated_at = NOW()
        WHERE id = #{taskId}
    </update>

    <!-- 更新已标注数量 -->
    <update id="updateAnnotatedCount">
        UPDATE faq_annotation_task
        SET annotated_count = #{annotatedCount},
            updated_at = NOW()
        WHERE id = #{taskId}
    </update>

</mapper>
