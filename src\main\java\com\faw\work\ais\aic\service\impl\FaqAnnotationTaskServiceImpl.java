package com.faw.work.ais.aic.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.faw.work.ais.aic.mapper.faq.FaqAnnotationTaskMapper;
import com.faw.work.ais.aic.model.domain.FaqAnnotationTaskPO;
import com.faw.work.ais.aic.service.FaqAnnotationTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * FAQ标注任务Service实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class FaqAnnotationTaskServiceImpl implements FaqAnnotationTaskService {

    @Autowired
    private FaqAnnotationTaskMapper faqAnnotationTaskMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createTask(FaqAnnotationTaskPO task) {
        if (ObjectUtil.isNull(task)) {
            throw new IllegalArgumentException("标注任务信息不能为空");
        }
        
        if (StrUtil.isBlank(task.getTaskName())) {
            throw new IllegalArgumentException("任务名称不能为空");
        }
        
        if (StrUtil.isBlank(task.getRobotId())) {
            throw new IllegalArgumentException("机器人ID不能为空");
        }
        
        if (StrUtil.isBlank(task.getCreatorId())) {
            throw new IllegalArgumentException("创建人ID不能为空");
        }

        // 设置默认值
        if (StrUtil.isBlank(task.getStatus())) {
            task.setStatus("processing");
        }
        
        if (ObjectUtil.isNull(task.getTotalCount())) {
            task.setTotalCount(0);
        }
        
        if (ObjectUtil.isNull(task.getAnnotatedCount())) {
            task.setAnnotatedCount(0);
        }
        
        task.setCreatedAt(LocalDateTime.now());
        task.setUpdatedAt(LocalDateTime.now());

        int result = faqAnnotationTaskMapper.insert(task);
        if (result > 0) {
            log.info("成功创建标注任务，任务ID: {}, 任务名称: {}", task.getId(), task.getTaskName());
            return task.getId();
        } else {
            throw new RuntimeException("创建标注任务失败");
        }
    }

    @Override
    public FaqAnnotationTaskPO getTaskById(String taskId) {
        if (StrUtil.isBlank(taskId)) {
            throw new IllegalArgumentException("任务ID不能为空");
        }
        
        return faqAnnotationTaskMapper.selectById(taskId);
    }

    @Override
    public List<FaqAnnotationTaskPO> getTasksByRobotId(String robotId) {
        if (StrUtil.isBlank(robotId)) {
            throw new IllegalArgumentException("机器人ID不能为空");
        }
        
        return faqAnnotationTaskMapper.findByRobotId(robotId);
    }

    @Override
    public List<FaqAnnotationTaskPO> getTasksByCreatorId(String creatorId) {
        if (StrUtil.isBlank(creatorId)) {
            throw new IllegalArgumentException("创建人ID不能为空");
        }
        
        return faqAnnotationTaskMapper.findByCreatorId(creatorId);
    }

    @Override
    public List<FaqAnnotationTaskPO> getTasksByStatus(String status) {
        if (StrUtil.isBlank(status)) {
            throw new IllegalArgumentException("任务状态不能为空");
        }
        
        return faqAnnotationTaskMapper.findByStatus(status);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean completeTask(String taskId) {
        if (StrUtil.isBlank(taskId)) {
            throw new IllegalArgumentException("任务ID不能为空");
        }
        
        int result = faqAnnotationTaskMapper.updateStatusToCompleted(taskId);
        if (result > 0) {
            log.info("成功完成标注任务，任务ID: {}", taskId);
            return true;
        }
        
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateAnnotatedCount(String taskId, Integer annotatedCount) {
        if (StrUtil.isBlank(taskId)) {
            throw new IllegalArgumentException("任务ID不能为空");
        }
        
        if (ObjectUtil.isNull(annotatedCount) || annotatedCount < 0) {
            throw new IllegalArgumentException("已标注数量不能为空且不能小于0");
        }
        
        int result = faqAnnotationTaskMapper.updateAnnotatedCount(taskId, annotatedCount);
        if (result > 0) {
            log.info("成功更新标注任务已标注数量，任务ID: {}, 已标注数量: {}", taskId, annotatedCount);
            return true;
        }
        
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteTask(String taskId) {
        if (StrUtil.isBlank(taskId)) {
            throw new IllegalArgumentException("任务ID不能为空");
        }
        
        int result = faqAnnotationTaskMapper.deleteById(taskId);
        if (result > 0) {
            log.info("成功删除标注任务，任务ID: {}", taskId);
            return true;
        }
        
        return false;
    }
}
