package com.faw.work.ais.aic.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * FAQ命中日志详情查询请求对象
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "FAQ命中日志详情查询请求对象")
public class FaqHitLogDetailQueryRequest {

    @Schema(description = "标注任务ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "标注任务ID不能为空")
    private String annotationTaskId;

    @Schema(description = "标注类型筛选：correct-正确，error-错误，uncovered-未覆盖，invalid-无效，pending-待定")
    private String annotationType;

    @Schema(description = "是否已锁定筛选：true-已锁定，false-未锁定")
    private Boolean isLocked;

    @Schema(description = "页码", defaultValue = "1")
    @NotNull(message = "页码不能为空")
    private Integer pageNum;

    @Schema(description = "每页大小", defaultValue = "10")
    @NotNull(message = "每页大小不能为空")
    private Integer pageSize;
}
