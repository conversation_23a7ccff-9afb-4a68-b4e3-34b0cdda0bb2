package com.faw.work.ais.aic.controller;

import com.faw.work.ais.aic.model.domain.FaqAnnotationDetailPO;
import com.faw.work.ais.aic.model.domain.FaqAnnotationTaskPO;
import com.faw.work.ais.aic.model.request.FaqAnnotationTaskCreateRequest;
import com.faw.work.ais.aic.model.request.FaqAnnotationTaskQueryRequest;
import com.faw.work.ais.aic.model.request.FaqHitLogAnnotationRequest;
import com.faw.work.ais.aic.model.request.FaqHitLogDetailQueryRequest;
import com.faw.work.ais.aic.model.response.FaqAnnotationStatisticsResponse;
import com.faw.work.ais.aic.model.response.FaqAnnotationTaskResponse;
import com.faw.work.ais.aic.model.response.FaqHitLogDetailResponse;
import com.faw.work.ais.aic.service.FaqAnnotationDetailService;
import com.faw.work.ais.aic.service.FaqAnnotationStatisticsService;
import com.faw.work.ais.aic.service.FaqAnnotationTaskService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * FAQ命中日志控制器
 * 提供FAQ标注任务相关的接口功能
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/faq-hit-log")
@Tag(name = "FAQ命中日志管理", description = "FAQ标注任务相关接口")
@Slf4j
public class FaqHitLogController {

    @Autowired
    private FaqAnnotationTaskService faqAnnotationTaskService;

    @Autowired
    private FaqAnnotationDetailService faqAnnotationDetailService;

    @Autowired
    private FaqAnnotationStatisticsService faqAnnotationStatisticsService;

    /**
     * 新建标注任务
     *
     * @param request 新建任务请求参数
     * @return 创建结果
     */
    @PostMapping("/annotation-task-create")
    @Operation(summary = "新建标注任务", description = "创建新的FAQ标注任务，抽取指定条件的数据")
    public String createAnnotationTask(@Valid @RequestBody FaqAnnotationTaskCreateRequest request) {
        log.info("开始创建标注任务，任务名称: {}", request.getTaskName());
        return faqAnnotationTaskService.createAnnotationTask(request);
    }

    /**
     * 任务标注列表查询
     *
     * @param request 查询请求参数
     * @return 任务列表
     */
    @PostMapping("/annotation-task-list")
    @Operation(summary = "任务标注列表查询", description = "查询标注任务列表，支持搜索和筛选")
    public List<FaqAnnotationTaskResponse> getAnnotationTaskList(@Valid @RequestBody FaqAnnotationTaskQueryRequest request) {
        log.info("查询标注任务列表，查询条件: {}", request);
        return faqAnnotationTaskService.getAnnotationTaskList(request);
    }

    /**
     * 任务详情列表查询
     *
     * @param request 详情查询请求参数
     * @return 任务详情数据
     */
    @PostMapping("/annotation-task-detail")
    @Operation(summary = "任务详情列表查询", description = "查询标注任务的详细数据列表")
    public List<FaqHitLogDetailResponse> getAnnotationTaskDetail(@Valid @RequestBody FaqHitLogDetailQueryRequest request) {
        log.info("查询标注任务详情，任务ID: {}", request.getAnnotationTaskId());
        return faqAnnotationDetailService.getTaskDetails(request);
    }

    /**
     * 数据统计查询
     *
     * @param taskId 任务ID
     * @return 统计数据
     */
    @GetMapping("/annotation-statistics/{taskId}")
    @Operation(summary = "数据统计查询", description = "查询标注任务的统计信息")
    public FaqAnnotationStatisticsResponse getAnnotationStatistics(@PathVariable String taskId) {
        log.info("查询标注统计数据，任务ID: {}", taskId);
        return faqAnnotationStatisticsService.getAnnotationStatistics(taskId);
    }

    /**
     * 任务标注（修改状态）
     *
     * @param request 标注请求参数
     * @return 标注结果
     */
    @PostMapping("/annotation-mark")
    @Operation(summary = "任务标注", description = "对单条数据进行标注操作")
    public Boolean markAnnotation(@Valid @RequestBody FaqHitLogAnnotationRequest request) {
        log.info("标注数据，详情ID: {}, 标注类型: {}", request.getHitLogId(), request.getAnnotationType());
        return faqAnnotationDetailService.markAnnotation(
                request.getHitLogId(),
                request.getAnnotationType(),
                request.getAnnotationSubtype(),
                request.getAnnotatorId(),
                request.getAnnotatorName()
        );
    }

    /**
     * 重新标注
     *
     * @param hitLogId 命中日志ID
     * @return 重新标注结果
     */
    @PostMapping("/annotation-unlock/{hitLogId}")
    @Operation(summary = "重新标注", description = "解锁已标注的数据，允许重新标注")
    public Boolean unlockAnnotation(@PathVariable String hitLogId) {
        log.info("重新标注数据，详情ID: {}", hitLogId);
        return faqAnnotationDetailService.unlockAnnotation(hitLogId);
    }

    /**
     * 完成标注任务
     *
     * @param taskId 任务ID
     * @return 完成结果
     */
    @PostMapping("/annotation-task-complete/{taskId}")
    @Operation(summary = "完成标注任务", description = "将标注任务状态修改为已完成")
    public Boolean completeAnnotationTask(@PathVariable String taskId) {
        log.info("完成标注任务，任务ID: {}", taskId);
        return faqAnnotationTaskService.completeTask(taskId);
    }

    /**
     * 删除标注任务
     *
     * @param taskId 任务ID
     * @return 删除结果
     */
    @PostMapping("/annotation-task-delete/{taskId}")
    @Operation(summary = "删除标注任务", description = "删除已完成的标注任务")
    public Boolean deleteAnnotationTask(@PathVariable String taskId) {
        log.info("删除标注任务，任务ID: {}", taskId);

        // 检查是否可以删除
        if (!faqAnnotationTaskService.canDeleteTask(taskId)) {
            throw new IllegalStateException("只有已完成的任务才能删除");
        }

        return faqAnnotationTaskService.deleteTask(taskId);
    }

    /**
     * 导出标注任务数据
     *
     * @param taskId 任务ID
     * @param response HTTP响应对象
     */
    @GetMapping("/annotation-task-export/{taskId}")
    @Operation(summary = "导出标注任务数据", description = "导出标注任务的Excel数据")
    public void exportAnnotationTask(@PathVariable String taskId, HttpServletResponse response) {
        log.info("导出标注任务数据，任务ID: {}", taskId);

        try {
            // 获取任务信息
            FaqAnnotationTaskPO taskPO = faqAnnotationTaskService.getTaskById(taskId);
            if (taskPO == null) {
                throw new IllegalArgumentException("任务不存在");
            }

            // 获取导出数据
            List<FaqAnnotationDetailPO> exportData = faqAnnotationDetailService.getAnnotationDataForExport(taskId);

            // 设置响应头
            String fileName = URLEncoder.encode("标注任务数据_" + taskPO.getTaskName() + "_" +
                    LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + ".xlsx",
                    StandardCharsets.UTF_8);
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename=" + fileName);

            // 创建Excel工作簿
            try (Workbook workbook = new XSSFWorkbook()) {
                Sheet sheet = workbook.createSheet("标注数据");

                // 创建表头
                Row headerRow = sheet.createRow(0);
                String[] headers = {
                    "任务名称", "机器人名称", "数据来源", "用户问法", "匹配类型",
                    "匹配答案", "FAQ标题", "匹配度分数", "标注类型", "标注子类型",
                    "标注时间", "标注人", "创建时间"
                };

                for (int i = 0; i < headers.length; i++) {
                    Cell cell = headerRow.createCell(i);
                    cell.setCellValue(headers[i]);
                }

                // 填充数据
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                for (int i = 0; i < exportData.size(); i++) {
                    FaqAnnotationDetailPO data = exportData.get(i);
                    Row row = sheet.createRow(i + 1);

                    row.createCell(0).setCellValue(taskPO.getTaskName());
                    row.createCell(1).setCellValue(taskPO.getRobotName());
                    row.createCell(2).setCellValue(getDataSourceName(taskPO.getDataSource()));
                    row.createCell(3).setCellValue(data.getUserQuestion() != null ? data.getUserQuestion() : "");
                    row.createCell(4).setCellValue(data.getMatchType() != null ? ("has_answer".equals(data.getMatchType()) ? "有答案" : "无答案") : "无答案");
                    row.createCell(5).setCellValue(data.getMatchedContent() != null ? data.getMatchedContent() : "");
                    row.createCell(6).setCellValue(data.getFaqTitle() != null ? data.getFaqTitle() : "");
                    row.createCell(7).setCellValue(data.getMatchScore() != null ? data.getMatchScore().toString() : "");
                    row.createCell(8).setCellValue(getAnnotationTypeName(data.getAnnotationType()));
                    row.createCell(9).setCellValue(getAnnotationSubtypeName(data.getAnnotationSubtype()));
                    row.createCell(10).setCellValue(data.getAnnotatedAt() != null ? data.getAnnotatedAt().format(formatter) : "");
                    row.createCell(11).setCellValue(data.getAnnotatorName() != null ? data.getAnnotatorName() : "");
                    row.createCell(12).setCellValue(data.getCreatedAt() != null ? data.getCreatedAt().format(formatter) : "");
                }

                // 自动调整列宽
                for (int i = 0; i < headers.length; i++) {
                    sheet.autoSizeColumn(i);
                }

                // 写入响应流
                workbook.write(response.getOutputStream());
                response.getOutputStream().flush();
            }

            log.info("成功导出标注任务数据，任务ID: {}, 数据量: {}", taskId, exportData.size());

        } catch (Exception e) {
            log.error("导出标注任务数据失败，任务ID: {}", taskId, e);
            throw new RuntimeException("导出失败: " + e.getMessage());
        }
    }

    /**
     * 获取数据来源名称
     */
    private String getDataSourceName(String dataSource) {
        switch (dataSource) {
            case "all": return "全部";
            case "prod": return "正式环境";
            case "test": return "测试环境";
            default: return dataSource;
        }
    }

    /**
     * 获取标注类型名称
     */
    private String getAnnotationTypeName(String annotationType) {
        if (annotationType == null) return "";
        switch (annotationType) {
            case "correct": return "正确";
            case "error": return "错误";
            case "uncovered": return "未覆盖";
            case "invalid": return "无效";
            case "pending": return "待定";
            default: return annotationType;
        }
    }

    /**
     * 获取标注子类型名称
     */
    private String getAnnotationSubtypeName(String annotationSubtype) {
        if (annotationSubtype == null) return "";
        switch (annotationSubtype) {
            case "error_no_handle": return "错误暂不处理";
            case "error_modify_knowledge": return "错误修改知识";
            case "error_handled": return "错误已处理";
            case "uncovered_no_handle": return "未覆盖暂不处理";
            case "uncovered_add_knowledge": return "未覆盖新增知识";
            case "uncovered_handled": return "未覆盖已处理";
            default: return annotationSubtype;
        }
    }
}
