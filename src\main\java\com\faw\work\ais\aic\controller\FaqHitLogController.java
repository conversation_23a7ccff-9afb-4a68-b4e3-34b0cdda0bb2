package com.faw.work.ais.aic.controller;

import com.alibaba.excel.EasyExcel;
import com.faw.work.ais.aic.common.base.AiResult;
import com.faw.work.ais.aic.model.domain.FaqAnnotationDetailPO;
import com.faw.work.ais.aic.model.domain.FaqAnnotationTaskPO;
import com.faw.work.ais.aic.model.dto.FaqAnnotationExportDTO;
import com.faw.work.ais.aic.model.request.FaqAnnotationTaskCreateRequest;
import com.faw.work.ais.aic.model.request.FaqAnnotationTaskQueryRequest;
import com.faw.work.ais.aic.model.request.FaqHitLogAnnotationRequest;
import com.faw.work.ais.aic.model.request.FaqHitLogDetailQueryRequest;
import com.faw.work.ais.aic.model.response.FaqAnnotationStatisticsResponse;
import com.faw.work.ais.aic.model.response.FaqAnnotationTaskResponse;
import com.faw.work.ais.aic.model.response.FaqHitLogDetailResponse;
import com.faw.work.ais.aic.service.FaqAnnotationDetailService;
import com.faw.work.ais.aic.service.FaqAnnotationStatisticsService;
import com.faw.work.ais.aic.service.FaqAnnotationTaskService;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/faq-hit-log")
@Tag(name = "FAQ命中日志管理", description = "FAQ标注任务相关接口")
@Slf4j
public class FaqHitLogController {

    @Autowired
    private FaqAnnotationTaskService faqAnnotationTaskService;

    @Autowired
    private FaqAnnotationDetailService faqAnnotationDetailService;

    @Autowired
    private FaqAnnotationStatisticsService faqAnnotationStatisticsService;

    @PostMapping("/annotation-task-create")
    @Operation(summary = "新建标注任务", description = "创建新的FAQ标注任务，抽取指定条件的数据")
    public AiResult<String> createAnnotationTask(@Valid @RequestBody FaqAnnotationTaskCreateRequest request) {
        log.info("开始创建标注任务，任务名称: {}", request.getTaskName());
        return AiResult.success(faqAnnotationTaskService.createAnnotationTask(request));
    }

    @PostMapping("/annotation-task-list")
    @Operation(summary = "任务标注列表查询", description = "查询标注任务列表，支持搜索和筛选")
    public AiResult<PageInfo<FaqAnnotationTaskResponse>> getAnnotationTaskList(@Valid @RequestBody FaqAnnotationTaskQueryRequest request) {
        log.info("查询标注任务列表，查询条件: {}", request);
        return AiResult.success(faqAnnotationTaskService.getAnnotationTaskList(request));
    }

    @PostMapping("/annotation-task-detail")
    @Operation(summary = "任务详情列表查询", description = "查询标注任务的详细数据列表")
    public AiResult<PageInfo<FaqHitLogDetailResponse>> getAnnotationTaskDetail(@Valid @RequestBody FaqHitLogDetailQueryRequest request) {
        log.info("查询标注任务详情，任务ID: {}", request.getAnnotationTaskId());
        return AiResult.success(faqAnnotationDetailService.getTaskDetails(request));
    }

    @GetMapping("/annotation-statistics/{taskId}")
    @Operation(summary = "数据统计查询", description = "查询标注任务的统计信息")
    public AiResult<FaqAnnotationStatisticsResponse> getAnnotationStatistics(@PathVariable String taskId) {
        log.info("查询标注统计数据，任务ID: {}", taskId);
        return AiResult.success(faqAnnotationStatisticsService.getAnnotationStatistics(taskId));
    }

    @PostMapping("/annotation-mark")
    @Operation(summary = "任务标注", description = "对单条或批量数据进行标注操作")
    public AiResult<Boolean> markAnnotation(@Valid @RequestBody List<FaqHitLogAnnotationRequest> requestList) {
        log.info("批量标注数据，数量: {}", requestList.size());

        for (FaqHitLogAnnotationRequest request : requestList) {
            log.info("标注数据，详情ID: {}, 标注类型: {}", request.getDetailId(), request.getAnnotationType());
            faqAnnotationDetailService.markAnnotation(
                    request.getDetailId(),
                    request.getAnnotationType(),
                    request.getAnnotationSubtype()
            );
        }

        return AiResult.success(true);
    }


    @PostMapping("/annotation-unlock/{detailId}")
    @Operation(summary = "重新标注", description = "解锁已标注的数据，允许重新标注")
    public AiResult<Boolean> unlockAnnotation(@PathVariable String detailId) {
        log.info("重新标注数据，详情ID: {}", detailId);
        return AiResult.success(faqAnnotationDetailService.unlockAnnotation(detailId));
    }

    @PostMapping("/annotation-task-complete/{taskId}")
    @Operation(summary = "完成标注任务", description = "将标注任务状态修改为已完成")
    public AiResult<Boolean> completeAnnotationTask(@PathVariable String taskId) {
        log.info("完成标注任务，任务ID: {}", taskId);
        return AiResult.success(faqAnnotationTaskService.completeTask(taskId));
    }

    @PostMapping("/annotation-task-delete/{taskId}")
    @Operation(summary = "删除标注任务", description = "删除已完成的标注任务")
    public AiResult<Boolean> deleteAnnotationTask(@PathVariable String taskId) {
        log.info("删除标注任务，任务ID: {}", taskId);

        if (!faqAnnotationTaskService.canDeleteTask(taskId)) {
            throw new IllegalStateException("只有已完成的任务才能删除");
        }

        return AiResult.success(faqAnnotationTaskService.deleteTask(taskId));
    }

    @GetMapping("/annotation-task-export/{taskId}")
    @Operation(summary = "导出标注任务数据", description = "导出标注任务的Excel数据")
    public void exportAnnotationTask(@PathVariable String taskId, HttpServletResponse response) {
        log.info("导出标注任务数据，任务ID: {}", taskId);

        try {
            // 获取任务信息
            FaqAnnotationTaskPO task = faqAnnotationTaskService.getTaskById(taskId);
            if (task == null) {
                throw new IllegalArgumentException("任务不存在");
            }

            // 获取导出数据
            List<FaqAnnotationDetailPO> exportData = faqAnnotationDetailService.getAnnotationDataForExport(taskId);

            // 转换为导出DTO
            List<FaqAnnotationExportDTO> exportDTOList = convertToExportDTO(exportData, task);

            // 设置响应头
            String fileName = URLEncoder.encode("标注任务数据_" + task.getTaskName() + "_" +
                            LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + ".xlsx",
                    StandardCharsets.UTF_8);

            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-Disposition", "attachment; filename=" + fileName);

            // 使用 EasyExcel 写入数据
            EasyExcel.write(response.getOutputStream(), FaqAnnotationExportDTO.class)
                    .sheet("标注数据")
                    .doWrite(exportDTOList);

            log.info("成功导出标注任务数据，任务ID: {}, 数据量: {}", taskId, exportData.size());

        } catch (Exception e) {
            log.error("导出标注任务数据失败，任务ID: {}", taskId, e);
            throw new RuntimeException("导出失败: " + e.getMessage());
        }
    }

    /**
     * 转换为导出DTO
     */
    private List<FaqAnnotationExportDTO> convertToExportDTO(List<FaqAnnotationDetailPO> exportData, FaqAnnotationTaskPO task) {
        List<FaqAnnotationExportDTO> exportDTOList = new ArrayList<>();

        for (FaqAnnotationDetailPO data : exportData) {
            FaqAnnotationExportDTO exportDTO = new FaqAnnotationExportDTO();

            // 设置任务相关信息
            exportDTO.setTaskName(task.getTaskName());
            exportDTO.setRobotName(task.getRobotName());
            exportDTO.setDataSourceName(getDataSourceName(task.getDataSource()));

            // 设置数据详情
            exportDTO.setUserQuestion(data.getUserQuestion() != null ? data.getUserQuestion() : "");
            exportDTO.setMatchTypeName(data.getMatchType() != null ?
                    ("has_answer".equals(data.getMatchType()) ? "有答案" : "无答案") : "无答案");
            exportDTO.setMatchedContent(data.getMatchedContent() != null ? data.getMatchedContent() : "");
            exportDTO.setFaqTitle(data.getFaqTitle() != null ? data.getFaqTitle() : "");
            exportDTO.setMatchScore(data.getMatchScore() != null ? data.getMatchScore().toString() : "");
            exportDTO.setAnnotationTypeName(getAnnotationTypeName(data.getAnnotationType()));
            exportDTO.setAnnotationSubtypeName(getAnnotationSubtypeName(data.getAnnotationSubtype()));
            exportDTO.setAnnotatedAt(data.getAnnotatedAt());
            exportDTO.setAnnotatorName(data.getAnnotatorName() != null ? data.getAnnotatorName() : "");
            exportDTO.setCreatedAt(data.getCreatedAt());

            exportDTOList.add(exportDTO);
        }

        return exportDTOList;
    }


    private String getDataSourceName(String dataSource) {
        return switch (dataSource) {
            case "all" -> "全部";
            case "prod" -> "正式环境";
            case "test" -> "测试环境";
            default -> dataSource;
        };
    }

    private String getAnnotationTypeName(String annotationType) {
        if (annotationType == null) {
            return "";
        }
        return switch (annotationType) {
            case "correct" -> "正确";
            case "error" -> "错误";
            case "uncovered" -> "未覆盖";
            case "invalid" -> "无效";
            case "pending" -> "待定";
            default -> annotationType;
        };
    }

    private String getAnnotationSubtypeName(String annotationSubtype) {
        if (annotationSubtype == null) {
            return "";
        }
        return switch (annotationSubtype) {
            case "error_no_handle" -> "错误暂不处理";
            case "error_modify_knowledge" -> "错误修改知识";
            case "error_handled" -> "错误已处理";
            case "uncovered_no_handle" -> "未覆盖暂不处理";
            case "uncovered_add_knowledge" -> "未覆盖新增知识";
            case "uncovered_handled" -> "未覆盖已处理";
            default -> annotationSubtype;
        };
    }
}
