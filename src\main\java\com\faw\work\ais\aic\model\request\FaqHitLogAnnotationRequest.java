package com.faw.work.ais.aic.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * FAQ命中日志标注请求对象
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "FAQ命中日志标注请求对象")
public class FaqHitLogAnnotationRequest {

    @Schema(description = "命中日志ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "命中日志ID不能为空")
    private String hitLogId;

    @Schema(description = "标注类型：correct-正确，error-错误，uncovered-未覆盖，invalid-无效，pending-待定", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "标注类型不能为空")
    private String annotationType;

    @Schema(description = "标注子类型：error_no_handle-错误暂不处理，error_modify_knowledge-错误修改知识，error_handled-错误已处理，uncovered_no_handle-未覆盖暂不处理，uncovered_add_knowledge-未覆盖新增知识，uncovered_handled-未覆盖已处理")
    private String annotationSubtype;

    @Schema(description = "标注人ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "标注人ID不能为空")
    private String annotatorId;

    @Schema(description = "标注人姓名", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "标注人姓名不能为空")
    private String annotatorName;
}
