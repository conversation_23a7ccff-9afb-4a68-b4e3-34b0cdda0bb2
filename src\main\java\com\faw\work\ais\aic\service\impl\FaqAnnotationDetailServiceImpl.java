package com.faw.work.ais.aic.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.faw.work.ais.aic.mapper.faq.FaqAnnotationDetailMapper;
import com.faw.work.ais.aic.model.domain.FaqAnnotationDetailPO;
import com.faw.work.ais.aic.model.request.FaqHitLogDetailQueryRequest;
import com.faw.work.ais.aic.model.response.FaqHitLogDetailResponse;
import com.faw.work.ais.aic.service.FaqAnnotationDetailService;
import com.faw.work.ais.aic.service.FaqAnnotationStatisticsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * FAQ标注详情Service实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class FaqAnnotationDetailServiceImpl implements FaqAnnotationDetailService {

    @Autowired
    private FaqAnnotationDetailMapper faqAnnotationDetailMapper;

    @Autowired
    private FaqAnnotationStatisticsService faqAnnotationStatisticsService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createDetail(FaqAnnotationDetailPO detail) {
        if (ObjectUtil.isNull(detail)) {
            throw new IllegalArgumentException("标注详情信息不能为空");
        }

        detail.setCreatedAt(LocalDateTime.now());
        detail.setUpdatedAt(LocalDateTime.now());

        int result = faqAnnotationDetailMapper.insert(detail);
        if (result > 0) {
            log.info("成功创建标注详情，详情ID: {}", detail.getId());
            return detail.getId();
        } else {
            throw new RuntimeException("创建标注详情失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchCreateDetails(List<FaqAnnotationDetailPO> details) {
        if (ObjectUtil.isNull(details) || details.isEmpty()) {
            throw new IllegalArgumentException("标注详情列表不能为空");
        }

        LocalDateTime now = LocalDateTime.now();
        for (FaqAnnotationDetailPO detail : details) {
            detail.setCreatedAt(now);
            detail.setUpdatedAt(now);
            if (ObjectUtil.isNull(detail.getIsLocked())) {
                detail.setIsLocked(false);
            }
        }

        int result = faqAnnotationDetailMapper.batchInsert(details);
        log.info("批量创建标注详情，数量: {}", result);
        return result;
    }

    @Override
    public FaqAnnotationDetailPO getDetailById(String detailId) {
        if (StrUtil.isBlank(detailId)) {
            throw new IllegalArgumentException("详情ID不能为空");
        }

        return faqAnnotationDetailMapper.selectById(detailId);
    }

    @Override
    public List<FaqHitLogDetailResponse> getTaskDetails(FaqHitLogDetailQueryRequest request) {
        if (StrUtil.isBlank(request.getAnnotationTaskId())) {
            throw new IllegalArgumentException("标注任务ID不能为空");
        }

        int offset = (request.getPageNum() - 1) * request.getPageSize();
        List<FaqAnnotationDetailPO> details = faqAnnotationDetailMapper.findByTaskId(
                request.getAnnotationTaskId(),
                request.getAnnotationType(),
                request.getIsLocked(),
                offset,
                request.getPageSize()
        );

        List<FaqHitLogDetailResponse> responses = new ArrayList<>();
        for (FaqAnnotationDetailPO detail : details) {
            FaqHitLogDetailResponse response = new FaqHitLogDetailResponse();
            BeanUtils.copyProperties(detail, response);
            
            // 设置匹配类型
            response.setMatchType(detail.getMatchType());
            // 设置其他字段
            response.setId(detail.getId());
            response.setUserQuestion(detail.getUserQuestion());
            response.setMatchedAnswer(detail.getMatchedContent());
            response.setFaqTitle(detail.getFaqTitle());
            response.setMatchScore(detail.getMatchScore());
            response.setAnnotationType(detail.getAnnotationType());
            response.setAnnotationSubtype(detail.getAnnotationSubtype());
            response.setIsLocked(detail.getIsLocked());
            response.setAnnotatorId(detail.getAnnotatorId());
            response.setAnnotatorName(detail.getAnnotatorName());
            response.setAnnotatedAt(detail.getAnnotatedAt());
            response.setCreatedAt(detail.getCreatedAt());
            response.setUpdatedAt(detail.getUpdatedAt());
            
            responses.add(response);
        }

        return responses;
    }

    @Override
    public int countByTaskId(String taskId) {
        if (StrUtil.isBlank(taskId)) {
            throw new IllegalArgumentException("任务ID不能为空");
        }

        return faqAnnotationDetailMapper.countByTaskId(taskId);
    }

    @Override
    public int countAnnotatedByTaskId(String taskId) {
        if (StrUtil.isBlank(taskId)) {
            throw new IllegalArgumentException("任务ID不能为空");
        }

        return faqAnnotationDetailMapper.countAnnotatedByTaskId(taskId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean markAnnotation(String detailId, String annotationType, String annotationSubtype, 
                                 String annotatorId, String annotatorName) {
        if (StrUtil.isBlank(detailId)) {
            throw new IllegalArgumentException("详情ID不能为空");
        }
        if (StrUtil.isBlank(annotationType)) {
            throw new IllegalArgumentException("标注类型不能为空");
        }
        if (StrUtil.isBlank(annotatorId)) {
            throw new IllegalArgumentException("标注人ID不能为空");
        }
        if (StrUtil.isBlank(annotatorName)) {
            throw new IllegalArgumentException("标注人姓名不能为空");
        }

        // 获取详情信息以便更新统计
        FaqAnnotationDetailPO detail = faqAnnotationDetailMapper.selectById(detailId);
        if (ObjectUtil.isNull(detail)) {
            throw new IllegalArgumentException("标注详情不存在");
        }

        // 如果之前已经标注过，需要减少旧的统计
        if (StrUtil.isNotBlank(detail.getAnnotationType())) {
            faqAnnotationStatisticsService.decrementStatistics(detail.getTaskId(), 
                    detail.getAnnotationType(), detail.getAnnotationSubtype());
        }

        int updateCount = faqAnnotationDetailMapper.updateAnnotation(detailId, annotationType, 
                annotationSubtype, annotatorId, annotatorName);
        
        if (updateCount > 0) {
            // 增加新的统计
            faqAnnotationStatisticsService.incrementStatistics(detail.getTaskId(), 
                    annotationType, annotationSubtype);
            
            log.info("成功标注数据，详情ID: {}, 标注类型: {}, 标注人: {}", detailId, annotationType, annotatorName);
            return true;
        }
        
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean unlockAnnotation(String detailId) {
        if (StrUtil.isBlank(detailId)) {
            throw new IllegalArgumentException("详情ID不能为空");
        }

        // 获取详情信息以便更新统计
        FaqAnnotationDetailPO detail = faqAnnotationDetailMapper.selectById(detailId);
        if (ObjectUtil.isNull(detail)) {
            throw new IllegalArgumentException("标注详情不存在");
        }

        // 如果之前已经标注过，需要减少统计
        if (StrUtil.isNotBlank(detail.getAnnotationType())) {
            faqAnnotationStatisticsService.decrementStatistics(detail.getTaskId(), 
                    detail.getAnnotationType(), detail.getAnnotationSubtype());
        }

        int updateCount = faqAnnotationDetailMapper.unlockAnnotation(detailId);
        if (updateCount > 0) {
            log.info("成功解锁标注数据，详情ID: {}", detailId);
            return true;
        }
        
        return false;
    }

    @Override
    public List<FaqAnnotationDetailPO> getAnnotationDataForExport(String taskId) {
        if (StrUtil.isBlank(taskId)) {
            throw new IllegalArgumentException("任务ID不能为空");
        }

        return faqAnnotationDetailMapper.findAllByTaskId(taskId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteByTaskId(String taskId) {
        if (StrUtil.isBlank(taskId)) {
            throw new IllegalArgumentException("任务ID不能为空");
        }

        // 由于外键约束，删除任务时会自动删除相关的标注详情
        // 这里主要是为了记录日志
        int count = countByTaskId(taskId);
        log.info("删除任务的标注详情，任务ID: {}, 数量: {}", taskId, count);
        return true;
    }
}
