package com.faw.work.ais.aic.model.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * FAQ标注统计响应对象
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "FAQ标注统计响应对象")
public class FaqAnnotationStatisticsResponse {

    @Schema(description = "标注情况：已完成标注的数据数量")
    private Integer annotatedCount;

    @Schema(description = "标注总量：总共抽取的数据数量")
    private Integer totalCount;

    @Schema(description = "标注列表：各种标注类型的统计")
    private List<AnnotationTypeStatistics> annotationList;

    /**
     * 标注类型统计内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "标注类型统计")
    public static class AnnotationTypeStatistics {

        @Schema(description = "标注类型：correct-正确，error-错误，uncovered-未覆盖，invalid-无效，pending-待定")
        private String annotationType;

        @Schema(description = "标注类型名称")
        private String annotationTypeName;

        @Schema(description = "该类型下的数据数量")
        private Integer count;

        @Schema(description = "子类型统计列表")
        private List<AnnotationSubtypeStatistics> subtypeList;
    }

    /**
     * 标注子类型统计内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "标注子类型统计")
    public static class AnnotationSubtypeStatistics {

        @Schema(description = "标注子类型")
        private String annotationSubtype;

        @Schema(description = "标注子类型名称")
        private String annotationSubtypeName;

        @Schema(description = "该子类型下的数据数量")
        private Integer count;
    }
}
