package com.faw.work.ais.aic.mapper.faq;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.faw.work.ais.aic.model.domain.FaqAnnotationDetailPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * FAQ标注详情Mapper接口
 * 提供FAQ标注详情相关的数据库操作
 *
 * <AUTHOR>
 */
@Mapper
public interface FaqAnnotationDetailMapper extends BaseMapper<FaqAnnotationDetailPO> {

    /**
     * 根据任务ID分页查询标注详情
     *
     * @param taskId 任务ID
     * @param annotationType 标注类型筛选
     * @param isLocked 是否锁定筛选
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 标注详情列表
     */
    List<FaqAnnotationDetailPO> findByTaskId(@Param("taskId") String taskId,
                                             @Param("annotationType") String annotationType,
                                             @Param("isLocked") Boolean isLocked,
                                             @Param("offset") Integer offset,
                                             @Param("limit") Integer limit);

    /**
     * 统计任务的总数据量
     *
     * @param taskId 任务ID
     * @return 总数据量
     */
    int countByTaskId(@Param("taskId") String taskId);

    /**
     * 统计任务的已标注数量
     *
     * @param taskId 任务ID
     * @return 已标注数量
     */
    int countAnnotatedByTaskId(@Param("taskId") String taskId);

    /**
     * 根据标注类型统计数量
     *
     * @param taskId 任务ID
     * @param annotationType 标注类型
     * @param annotationSubtype 标注子类型
     * @return 统计数量
     */
    int countByAnnotationType(@Param("taskId") String taskId,
                             @Param("annotationType") String annotationType,
                             @Param("annotationSubtype") String annotationSubtype);

    /**
     * 更新标注信息
     *
     * @param id 标注详情ID
     * @param annotationType 标注类型
     * @param annotationSubtype 标注子类型
     * @param annotatorId 标注人ID
     * @param annotatorName 标注人姓名
     * @return 更新的记录数量
     */
    int updateAnnotation(@Param("id") String id,
                        @Param("annotationType") String annotationType,
                        @Param("annotationSubtype") String annotationSubtype,
                        @Param("annotatorId") String annotatorId,
                        @Param("annotatorName") String annotatorName);

    /**
     * 解锁标注（重新标注）
     *
     * @param id 标注详情ID
     * @return 更新的记录数量
     */
    int unlockAnnotation(@Param("id") String id);

    /**
     * 根据任务ID查询所有标注数据
     *
     * @param taskId 任务ID
     * @return 标注数据列表
     */
    List<FaqAnnotationDetailPO> findAllByTaskId(@Param("taskId") String taskId);

    /**
     * 批量插入标注详情
     *
     * @param details 标注详情列表
     * @return 插入的记录数量
     */
    int batchInsert(@Param("details") List<FaqAnnotationDetailPO> details);
}
